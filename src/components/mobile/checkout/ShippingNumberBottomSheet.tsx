import React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';
import { I18nManager, Keyboard, Platform, StyleSheet, Text, View } from 'react-native';
import BottomSheet, { BottomSheetBackdrop, BottomSheetBackdropProps, BottomSheetTextInput, BottomSheetView } from '@gorhom/bottom-sheet';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { PrimaryButton } from '../../common';
import { MobileIcon } from '../../../assets/svgs/icons';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../constants';
import { useAppSelector } from '../../../redux/hooks';
import { colors, fonts } from '../../../utils/theme';
import { validatePhoneNumber } from '../../../utils/functions';
import { getTenantCountry } from '../../../redux/selectors';

type Props = {
	onSelect: (countryCode: string, mobileNumber: string) => void;
};

const ShippingNumberBottomSheet = forwardRef(({ onSelect }: Props, ref) => {
	const { t } = useTranslation();
	const { bottom } = useSafeAreaInsets();
	const tenantCountry = useAppSelector(getTenantCountry);
	const bottomSheetModalRef = useRef<BottomSheet>(null);
	const [mobile, setMobile] = useState({ value: '', error: false, isValid: false });

	useImperativeHandle(ref, () => ({
		showShippingNumberBottomSheet: () => {
			bottomSheetModalRef.current?.expand();
		}
	}));

	const onChangeNumber = (text: string) => {
		const mobileNumber = text.replace(/[^0-9]/g, '');
		const isValid = validatePhoneNumber(tenantCountry.country_code, mobileNumber);
		setMobile({ value: mobileNumber, error: false, isValid: !!isValid });
	};

	/* Pass selected location, address details and mobile number to add customer screen */
	const onSubmitNumber = () => {
		Keyboard.dismiss();
		// Small delay to ensure keyboard dismissal completes before closing bottom sheet
		setTimeout(() => {
			bottomSheetModalRef.current?.close();
			onSelect(tenantCountry.country_code, mobile.value);
		}, 100);
	};

	const handleBackdropPress = useCallback(() => {
		Keyboard.dismiss();
		// Small delay to ensure keyboard dismissal completes before closing bottom sheet
		setTimeout(() => {
			bottomSheetModalRef.current?.close();
		}, 100);
	}, []);

	const renderBackdrop = useCallback((bprops: BottomSheetBackdropProps) => <BottomSheetBackdrop
		{...bprops}
		disappearsOnIndex={-1}
		appearsOnIndex={0}
		onPress={handleBackdropPress}
	/>, [handleBackdropPress]);

	return (
		<BottomSheet
			index={-1}
			ref={bottomSheetModalRef}
			backdropComponent={renderBackdrop}
			animateOnMount={true}
			handleIndicatorStyle={styles.bottomSheetIndicator}
			android_keyboardInputMode="adjustResize"
			enableDynamicSizing={true}
		>
			<BottomSheetView
				style={[styles.bottomSheet, bottom > 0 && { paddingBottom: bottom }, Platform.OS === 'android' && { paddingBottom: VERTICAL_DIMENS._10 }]}
			>
				<Text style={styles.title}>{t('shipping_mobile_number')}</Text>
				<Text style={styles.description}>{t('shipping_number_required')}</Text>
				<View style={styles.inputContainer}>
					<MobileIcon />
					<Text style={styles.countryCodeText}>{tenantCountry.country_code}</Text>
					<BottomSheetTextInput
						style={styles.mobileInput}
						keyboardType="number-pad"
						returnKeyType="done"
						value={mobile.value}
						onChangeText={onChangeNumber}
					/>
				</View>
				<PrimaryButton
					title={t('confirm')}
					onPress={onSubmitNumber}
					style={styles.nextBtn}
					titleStyle={styles.nextBtnText}
					disabled={!mobile.isValid}
				/>
			</BottomSheetView>
		</BottomSheet>
	);
});

const styles = StyleSheet.create({
	modalContent: {
		backgroundColor: colors.grey100
	},
	bottomSheet: {
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	bottomSheetIndicator: {
		backgroundColor: colors.grey300
	},
	title: {
		alignSelf: 'center',
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		marginBottom: VERTICAL_DIMENS._16,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	description: {
		alignSelf: 'center',
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._16,
		marginBottom: VERTICAL_DIMENS._16,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	inputContainer: {
		alignItems: 'center',
		flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1
	},
	countryCodeText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._16,
		marginLeft: HORIZONTAL_DIMENS._16
	},
	mobileInput: {
		flex: 1,
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._16,
		height: VERTICAL_DIMENS._48,
		paddingLeft: HORIZONTAL_DIMENS._10
	},
	nextBtn: {
		marginTop: VERTICAL_DIMENS._20,
		marginHorizontal: HORIZONTAL_DIMENS._24
	},
	nextBtnText: {
		textTransform: 'uppercase'
	}
});

export { ShippingNumberBottomSheet };
